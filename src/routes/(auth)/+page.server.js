import { redirect } from '@sveltejs/kit';

/** @type {import('./$types').PageServerLoad} */
export async function load({ locals }) {
    // If user is authenticated, redirect to dashboard
    if (locals.user) {
        throw redirect(307, '/dashboard');
    }
    
    // Return data for unauthenticated users
    return {
        authenticated: false,
        turnstileSiteKey: process.env.PUBLIC_TURNSTILE_SITE_KEY || '1x00000000000000000000AA'
    };
}
