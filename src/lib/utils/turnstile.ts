/**
 * Turnstile server-side verification utility
 * Validates Turnstile tokens against Cloudflare's API
 */

export interface TurnstileVerificationResult {
  success: boolean;
  challenge_ts?: string;
  hostname?: string;
  'error-codes'?: string[];
  action?: string;
  cdata?: string;
}

/**
 * Verify a Turnstile token on the server side
 * @param token - The Turnstile token from the client
 * @param remoteip - Optional: The visitor's IP address
 * @returns Promise<TurnstileVerificationResult>
 */
export async function verifyTurnstileToken(
  token: string,
  remoteip?: string
): Promise<TurnstileVerificationResult> {
  const secretKey = process.env.TURNSTILE_SECRET_KEY;
  
  if (!secretKey) {
    throw new Error('TURNSTILE_SECRET_KEY environment variable is not set');
  }

  if (!token) {
    return {
      success: false,
      'error-codes': ['missing-input-response']
    };
  }

  const formData = new FormData();
  formData.append('secret', secretKey);
  formData.append('response', token);
  
  if (remoteip) {
    formData.append('remoteip', remoteip);
  }

  try {
    const response = await fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: TurnstileVerificationResult = await response.json();
    return result;
  } catch (error) {
    console.error('Turnstile verification error:', error);
    return {
      success: false,
      'error-codes': ['internal-error']
    };
  }
}

/**
 * Middleware to verify Turnstile token in SvelteKit actions
 * @param token - The Turnstile token from the client
 * @param request - The SvelteKit request object (for IP extraction)
 * @returns Promise<boolean> - true if verification passed
 */
export async function verifyTurnstileMiddleware(
  token: string,
  request: Request
): Promise<boolean> {
  // Extract IP from request headers (Cloudflare sets CF-Connecting-IP)
  const remoteip = request.headers.get('CF-Connecting-IP') || 
                   request.headers.get('X-Forwarded-For') || 
                   request.headers.get('X-Real-IP') || 
                   undefined;

  const result = await verifyTurnstileToken(token, remoteip);
  
  if (!result.success) {
    console.warn('Turnstile verification failed:', result['error-codes']);
    return false;
  }

  return true;
}

/**
 * Get error message from Turnstile error codes
 * @param errorCodes - Array of error codes from Turnstile response
 * @returns Human-readable error message
 */
export function getTurnstileErrorMessage(errorCodes?: string[]): string {
  if (!errorCodes || errorCodes.length === 0) {
    return 'Unknown verification error';
  }

  const errorMessages: Record<string, string> = {
    'missing-input-secret': 'Missing secret key',
    'invalid-input-secret': 'Invalid secret key',
    'missing-input-response': 'Missing verification token',
    'invalid-input-response': 'Invalid verification token',
    'bad-request': 'Bad request format',
    'timeout-or-duplicate': 'Token expired or already used',
    'internal-error': 'Internal verification error'
  };

  const firstError = errorCodes[0];
  return errorMessages[firstError] || `Unknown error: ${firstError}`;
}
