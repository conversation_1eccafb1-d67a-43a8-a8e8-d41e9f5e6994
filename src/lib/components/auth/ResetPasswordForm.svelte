<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { authActions } from '$lib/stores/auth.js';
  import toast from 'svelte-5-french-toast';
  import { onMount } from 'svelte';
  
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import { Eye, EyeOff } from 'lucide-svelte';
  import Turnstile from '$lib/components/ui/Turnstile.svelte';

  let password = '';
  let confirmPassword = '';
  let showPassword = false;
  let showConfirmPassword = false;
  let isLoading = false;
  let token = '';

  // Turnstile state
  let turnstileToken = '';
  let isTurnstileVerified = false;

  onMount(() => {
    token = $page.url.searchParams.get('token') || '';
    
    if (!token) {
      toast.error('Invalid reset link');
      goto('/auth/forgot-password');
    }
  });

  // Handle Turnstile verification
  function handleTurnstileVerified(token: string) {
    turnstileToken = token;
    isTurnstileVerified = true;
    toast.success('Security verification completed');
  }

  function handleTurnstileError() {
    console.error('Turnstile error');
    toast.error('Security verification failed. Please try again.');
    isTurnstileVerified = false;
    turnstileToken = '';
  }

  function handleTurnstileExpired() {
    isTurnstileVerified = false;
    turnstileToken = '';
    toast.error('Security verification expired. Please verify again.');
  }

  async function handleSubmit(event: Event) {
    event.preventDefault();
    
    if (!password || !confirmPassword) {
      toast.error('Please fill in all fields');
      return;
    }

    if (password !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    if (password.length < 8) {
      toast.error('Password must be at least 9 characters long');
      return;
    }

    if (!isTurnstileVerified || !turnstileToken) {
      toast.error('Please complete security verification first');
      return;
    }

    isLoading = true;

    try {
      const result = await authActions.resetPassword(password, token);
      
      if (result.error) {
        toast.error(result.error.message || 'Failed to reset password');
        // Reset Turnstile on failure
        isTurnstileVerified = false;
        turnstileToken = '';
        return;
      }

      toast.success('Password reset successfully! You can now sign in with your new password.');
      goto('/');
      
    } catch (error) {
      console.error('Reset password error:', error);
      toast.error('An unexpected error occurred');
      // Reset Turnstile on error
      isTurnstileVerified = false;
      turnstileToken = '';
    } finally {
      isLoading = false;
    }
  }
</script>

<svelte:head>
  <title>Reset Password - SourceFlex</title>
</svelte:head>

<div class="reset-password-form">
  <div class="form-container">
    <div class="form-header">
      <h2 class="form-title">Set new password</h2>
      <p class="form-subtitle">
        Enter your new password below
      </p>
    </div>

    <form class="auth-form" onsubmit={handleSubmit}>
      <!-- Password Field -->
      <div class="form-group">
        <Label for="password">New Password</Label>
        <div class="password-wrapper">
          <Input
            id="password"
            type={showPassword ? 'text' : 'password'}
            bind:value={password}
            placeholder="Enter new password"
            disabled={isLoading}
            required
            class="pr-10"
          />
          <button
            type="button"
            class="password-toggle"
            onclick={() => showPassword = !showPassword}
          >
            {#if showPassword}
              <EyeOff class="h-4 w-4" />
            {:else}
              <Eye class="h-4 w-4" />
            {/if}
          </button>
        </div>
        <p class="field-hint">
          Must be at least 8 characters long
        </p>
      </div>

      <!-- Confirm Password Field -->
      <div class="form-group">
        <Label for="confirm-password">Confirm New Password</Label>
        <div class="password-wrapper">
          <Input
            id="confirm-password"
            type={showConfirmPassword ? 'text' : 'password'}
            bind:value={confirmPassword}
            placeholder="Confirm new password"
            disabled={isLoading}
            required
            class="pr-10"
          />
          <button
            type="button"
            class="password-toggle"
            onclick={() => showConfirmPassword = !showConfirmPassword}
          >
            {#if showConfirmPassword}
              <EyeOff class="h-4 w-4" />
            {:else}
              <Eye class="h-4 w-4" />
            {/if}
          </button>
        </div>
      </div>

      <!-- Turnstile Security Verification -->
      <div class="security-section">
        <div class="security-header">
          <h3 class="security-title">Security Verification</h3>
          <p class="security-subtitle">Please verify you're human to reset your password.</p>
        </div>
        <Turnstile
          onSuccess={handleTurnstileVerified}
          onError={handleTurnstileError}
          onExpired={handleTurnstileExpired}
        />
      </div>

      <Button type="submit" class="w-full submit-button" disabled={isLoading || !token || !isTurnstileVerified}>
        {#if isLoading}
          <div class="loading-spinner"></div>
          Updating password...
        {:else}
          Update password
        {/if}
      </Button>

      <div class="back-link">
        <a href="/" class="back-link-text">
          Back to login
        </a>
      </div>
    </form>
  </div>
</div>

<style>
  .reset-password-form {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    position: relative;
    z-index: 10;
  }

  .form-container {
    width: 100%;
    max-width: 28rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(12px);
    border-radius: 16px;
    padding: 2.5rem;
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: formSlideIn 0.8s ease-out;
  }

  @keyframes formSlideIn {
    from {
      opacity: 0;
      transform: translateY(30px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .form-header {
    text-align: center;
    margin-bottom: 2rem;
    animation: headerFadeIn 1s ease-out 0.2s both;
  }

  @keyframes headerFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .form-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: hsl(var(--foreground));
    margin-bottom: 0.5rem;
    letter-spacing: -0.025em;
  }

  .form-subtitle {
    font-size: 0.875rem;
    color: hsl(var(--muted-foreground));
    line-height: 1.5;
  }

  .auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    animation: fieldSlideIn 0.6s ease-out;
  }

  @keyframes fieldSlideIn {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .password-wrapper {
    position: relative;
    transition: all 0.3s ease;
  }

  .password-wrapper:focus-within {
    transform: translateY(-1px);
  }

  .password-toggle {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: hsl(var(--muted-foreground));
    cursor: pointer;
    padding: 0.375rem;
    border-radius: 6px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .password-toggle:hover {
    color: hsl(var(--foreground));
    background: linear-gradient(135deg, hsl(var(--muted)), rgba(0, 0, 0, 0.05));
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .password-toggle:active {
    transform: translateY(-50%) scale(0.95);
  }

  .field-hint {
    font-size: 0.75rem;
    color: hsl(var(--muted-foreground));
    margin-top: -0.25rem;
  }

  .security-section {
    border-top: 1px solid hsl(var(--border));
    padding-top: 1.5rem;
    margin-top: 0.5rem;
  }

  .security-header {
    text-align: center;
    margin-bottom: 1rem;
  }

  .security-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: hsl(var(--foreground));
    margin-bottom: 0.25rem;
  }

  .security-subtitle {
    font-size: 0.75rem;
    color: hsl(var(--muted-foreground));
    line-height: 1.4;
  }

  :global(.submit-button) {
    margin-top: 0.5rem;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  :global(.submit-button:hover:not(:disabled)) {
    transform: translateY(-1px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  :global(.submit-button:active:not(:disabled)) {
    transform: translateY(0);
  }

  .loading-spinner {
    width: 18px;
    height: 18px;
    border: 2.5px solid transparent;
    border-top: 2.5px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.75rem;
    display: inline-block;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  .back-link {
    text-align: center;
    margin-top: 0.5rem;
  }

  .back-link-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: hsl(var(--primary));
    text-decoration: none;
    transition: all 0.3s ease;
  }

  .back-link-text:hover {
    text-decoration: underline;
    opacity: 0.8;
  }

  /* Mobile Responsiveness */
  @media (max-width: 640px) {
    .reset-password-form {
      padding: 1.5rem 1rem;
    }

    .form-container {
      padding: 2rem 1.5rem;
    }

    .form-title {
      font-size: 1.5rem;
    }
  }
</style>
