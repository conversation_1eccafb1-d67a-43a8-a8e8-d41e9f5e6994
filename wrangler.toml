# Cloudflare Workers configuration for SourceFlex
name = "sourceflex"
main = ".svelte-kit/cloudflare/_worker.js"
compatibility_date = "2024-07-17"
compatibility_flags = ["nodejs_compat"]

# Static assets configuration for Workers
[assets]
directory = ".svelte-kit/cloudflare"
binding = "ASSETS"

# Environment variables for production
[vars]
NODE_ENV = "production"

# Production secrets (configure these in Cloudflare Dashboard > Workers > Settings > Variables)
# Do not put production secrets in this file!
# PUBLIC_TURNSTILE_SITE_KEY = "configure-in-dashboard"
# TURNSTILE_SECRET_KEY = "configure-in-dashboard"
# NHOST_ADMIN_SECRET = "configure-in-dashboard"

# Development environment for local testing
[env.development]
vars = { NODE_ENV = "development", PUBLIC_TURNSTILE_SITE_KEY = "1x00000000000000000000AA", TURNSTILE_SECRET_KEY = "1x0000000000000000000000000000000AA" }

# Staging environment (optional)
[env.staging]
vars = { NODE_ENV = "staging" }
# Configure staging secrets in Cloudflare Dashboard

# Observability configuration for monitoring and debugging
[observability.logs]
enabled = true

# Optional: Custom domain configuration (configure later in dashboard)
# [route]
# pattern = "sourceflex.io"
# zone_name = "sourceflex.io"
